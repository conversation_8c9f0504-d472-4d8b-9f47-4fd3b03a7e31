#!/usr/bin/env ts-node

/**
 * 合并分段生成的CSV文件
 * 使用方法: ts-node script/merge_csv.ts [期数]
 */

import * as fs from 'fs'
import * as path from 'path'

async function mergeCSVFiles() {
  try {
    const courseNo = parseInt(process.argv[2]) || 78
    
    console.log(`🔗 开始合并第${courseNo}期的CSV文件...`)
    
    const outputDir = path.join(process.cwd(), 'output')
    
    if (!fs.existsSync(outputDir)) {
      console.log(`❌ 输出目录不存在: ${outputDir}`)
      return
    }
    
    // 查找所有相关的CSV文件
    const files = fs.readdirSync(outputDir)
    const csvFiles = files
      .filter(file => file.startsWith(`course_${courseNo}_batch_`) && file.endsWith('.csv'))
      .sort() // 按文件名排序
    
    console.log(`📁 找到 ${csvFiles.length} 个CSV文件:`)
    csvFiles.forEach((file, index) => {
      console.log(`   ${index + 1}. ${file}`)
    })
    
    if (csvFiles.length === 0) {
      console.log(`❌ 没有找到第${courseNo}期的批次CSV文件`)
      console.log(`   文件名格式应为: course_${courseNo}_batch_*.csv`)
      return
    }
    
    // 准备合并后的文件
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19)
    const mergedFilePath = path.join(outputDir, `course_${courseNo}_merged_${timestamp}.csv`)
    
    let mergedContent = ''
    let totalRows = 0
    let headerWritten = false
    
    console.log(`\n🔄 开始合并文件...`)
    
    for (let i = 0; i < csvFiles.length; i++) {
      const filePath = path.join(outputDir, csvFiles[i])
      const fileContent = fs.readFileSync(filePath, 'utf8')
      const lines = fileContent.split('\n').filter(line => line.trim())
      
      console.log(`   处理文件 ${i + 1}/${csvFiles.length}: ${csvFiles[i]} (${lines.length - 1} 行数据)`)
      
      if (!headerWritten) {
        // 第一个文件，包含表头
        mergedContent += lines.join('\n') + '\n'
        totalRows += lines.length - 1 // 减去表头
        headerWritten = true
      } else {
        // 后续文件，跳过表头
        const dataLines = lines.slice(1) // 跳过第一行表头
        if (dataLines.length > 0) {
          mergedContent += dataLines.join('\n') + '\n'
          totalRows += dataLines.length
        }
      }
    }
    
    // 写入合并后的文件
    fs.writeFileSync(mergedFilePath, mergedContent, 'utf8')
    
    // 统计信息
    const fileSize = (fs.statSync(mergedFilePath).size / 1024).toFixed(2)
    
    console.log(`\n✅ CSV文件合并完成!`)
    console.log(`📁 合并后文件: ${mergedFilePath}`)
    console.log(`📊 合并统计:`)
    console.log(`   - 源文件数: ${csvFiles.length}`)
    console.log(`   - 总数据行: ${totalRows}`)
    console.log(`   - 文件大小: ${fileSize} KB`)
    
    // 显示前几行预览
    console.log(`\n📋 合并文件预览:`)
    const previewLines = mergedContent.split('\n').slice(0, 4)
    previewLines.forEach((line, index) => {
      if (line.trim()) {
        const displayLine = line.length > 120 ? line.substring(0, 120) + '...' : line
        console.log(`${index === 0 ? '表头' : `数据${index}`}: ${displayLine}`)
      }
    })
    
    // 验证数据完整性
    console.log(`\n🔍 数据完整性检查:`)
    const allLines = mergedContent.split('\n').filter(line => line.trim())
    const successRows = allLines.filter(line => !line.includes('ERROR')).length - 1 // 减去表头
    const errorRows = totalRows - successRows
    
    console.log(`   - 成功处理: ${successRows} 行`)
    console.log(`   - 处理失败: ${errorRows} 行`)
    console.log(`   - 成功率: ${((successRows / totalRows) * 100).toFixed(1)}%`)
    
    // 清理建议
    console.log(`\n🧹 清理建议:`)
    console.log(`- 合并完成后，可以删除原始批次文件以节省空间`)
    console.log(`- 删除命令: rm output/course_${courseNo}_batch_*.csv`)
    console.log(`- 或者移动到备份目录: mkdir output/backup && mv output/course_${courseNo}_batch_*.csv output/backup/`)
    
    console.log(`\n🎯 使用建议:`)
    console.log(`- 可以用Excel打开合并后的CSV文件进行数据分析`)
    console.log(`- 文件路径: ${mergedFilePath}`)
    
  } catch (error) {
    console.error('❌ 合并CSV文件失败:', error)
    process.exit(1)
  }
}

// 运行脚本
if (require.main === module) {
  console.log('🔗 CSV文件合并工具')
  console.log('使用方法: ts-node script/merge_csv.ts [期数]')
  console.log('例如: ts-node script/merge_csv.ts 78\n')
  
  mergeCSVFiles().catch(console.error)
}

export { mergeCSVFiles }
