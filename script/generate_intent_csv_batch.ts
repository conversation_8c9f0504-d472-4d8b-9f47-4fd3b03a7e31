#!/usr/bin/env ts-node

/**
 * 分段并行生成累积意向度CSV报告的脚本
 * 支持分段处理大量客户，避免一次性处理太多导致失败
 *
 * 使用方法: ts-node script/generate_intent_csv_batch.ts [期数] [开始位置] [数量] [并发数]
 *
 * 分段处理1500个客户的示例:
 *   第1段(前500): ts-node script/generate_intent_csv_batch.ts 78 0 500 50
 *   第2段(中500): ts-node script/generate_intent_csv_batch.ts 78 500 500 50
 *   第3段(后500): ts-node script/generate_intent_csv_batch.ts 78 1000 500 50
 *
 * 最后合并: ts-node script/merge_csv.ts 78
 */

import { NewIntentCalculator } from '../bot/service/moer/components/signals/new_intent_score'
import { DataService } from '../bot/service/moer/getter/getData'
import { ChatStatStoreManager } from '../bot/service/moer/storage/chat_state_store'
import * as fs from 'fs'
import * as path from 'path'

async function generateIntentCSVBatch() {
  try {
    // 从命令行参数获取配置
    const courseNo = parseInt(process.argv[2], 10) || 78
    const startIndex = parseInt(process.argv[3], 10) || 0     // 开始位置，默认0
    const batchSize = parseInt(process.argv[4], 10) || 500    // 本次处理数量，默认500
    const concurrency = parseInt(process.argv[5], 10) || 50   // 并发数，默认50

    const endIndex = startIndex + batchSize

    console.log(`🚀 分段生成第${courseNo}期客户累积意向度CSV报告`)
    console.log(`📊 处理范围: 第${startIndex + 1} - ${endIndex}个客户 (共${batchSize}个)`)
    console.log(`⚡ 并发数: ${concurrency}`)
    console.log(`⏰ 开始时间: ${new Date().toLocaleString()}\n`)

    // 创建输出目录
    const outputDir = path.join(process.cwd(), 'output')
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }

    // 获取客户列表
    const allChats = await DataService.getChatsByCourseNo(courseNo)
    console.log(`📋 总客户数: ${allChats.length}`)

    if (allChats.length === 0) {
      console.log(`❌ 没有找到第${courseNo}期的客户`)
      return
    }

    // 检查范围是否有效
    if (startIndex >= allChats.length) {
      console.log(`❌ 开始位置${startIndex}超出总客户数${allChats.length}`)
      return
    }

    // 获取当前批次的客户
    const batchChats = allChats.slice(startIndex, endIndex)
    console.log(`📝 实际处理: ${batchChats.length} 个客户`)
    console.log(`   范围: ${startIndex + 1} - ${startIndex + batchChats.length}\n`)

    // 准备CSV文件
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19)
    const batchLabel = `batch_${startIndex + 1}-${startIndex + batchChats.length}`
    const csvFilePath = path.join(outputDir, `course_${courseNo}_${batchLabel}_${timestamp}.csv`)

    // CSV表头
    const headers = [
      '客户ID',
      '微信名',
      '接量期分数',
      '第1天增量',
      '第1天累积',
      '第2天增量',
      '第2天累积',
      '第3天增量',
      '第3天累积',
      '第4天增量',
      '第4天累积',
      '总增量',
      '是否下单',
      '处理状态'
    ]

    // 写入表头
    let csvContent = `${headers.join(',')  }\n`

    // 分批并行处理
    const results: string[] = []
    const totalBatches = Math.ceil(batchChats.length / concurrency)
    let successCount = 0
    let errorCount = 0

    for (let batchStart = 0; batchStart < batchChats.length; batchStart += concurrency) {
      const batch = batchChats.slice(batchStart, batchStart + concurrency)
      const batchNumber = Math.floor(batchStart / concurrency) + 1

      console.log(`🚀 处理第 ${batchNumber}/${totalBatches} 小批 (${batch.length} 个客户)`)

      const batchStartTime = Date.now()

      // 并行处理当前小批次的所有客户
      const batchPromises = batch.map(async (chat, index) => {
        const globalIndex = startIndex + batchStart + index + 1
        const chatId = chat.id
        const chatName = chat.contact.wx_name || chat.id

        try {
          // 初始化客户状态
          await ChatStatStoreManager.initState(chatId)

          // 计算接量期意向度
          const preCourseResult = await NewIntentCalculator.calculatePreCourseIntentScore(chatId)

          // 存储结果
          const customerResults: {
            preCourse: any,
            days: any[],
            increments: number[],
            hasPurchased: boolean
          } = {
            preCourse: preCourseResult,
            days: [],
            increments: [],
            hasPurchased: false
          }

          // 计算各天数据
          for (let day = 1; day <= 4; day++) {
            try {
              // 计算累积意向度
              const cumulativeResult = await NewIntentCalculator.calculateCumulativeIntentScore(chatId, day)

              // 计算增量
              const dayIncrement = await (NewIntentCalculator as any).calculateSingleDayIntentScore(chatId, day)

              // 检查是否下单（仅第4天）
              if (day === 4) {
                customerResults.hasPurchased = await (NewIntentCalculator as any).checkHasPurchased(chatId)
              }

              customerResults.days.push(cumulativeResult)
              customerResults.increments.push(dayIncrement.intent_score as number)

            } catch (error) {
              customerResults.days.push({ intent_score: 'ERROR', intent_level: 'ERROR' })
              customerResults.increments.push(0)
            }
          }

          // 计算总增量
          const totalIncrement = customerResults.increments.reduce((sum, inc) => sum + inc, 0)

          // 构建CSV行
          const csvRow = [
            chatId,
            `"${chatName}"`,
            customerResults.preCourse.intent_score,
            `"${customerResults.preCourse.intent_level}"`,
            customerResults.increments[0] || 0,
            customerResults.days[0]?.intent_score || 'ERROR',
            `"${customerResults.days[0]?.intent_level || 'ERROR'}"`,
            customerResults.increments[1] || 0,
            customerResults.days[1]?.intent_score || 'ERROR',
            `"${customerResults.days[1]?.intent_level || 'ERROR'}"`,
            customerResults.increments[2] || 0,
            customerResults.days[2]?.intent_score || 'ERROR',
            `"${customerResults.days[2]?.intent_level || 'ERROR'}"`,
            customerResults.increments[3] || 0,
            customerResults.days[3]?.intent_score || 'ERROR',
            `"${customerResults.days[3]?.intent_level || 'ERROR'}"`,
            totalIncrement,
            customerResults.hasPurchased ? 'YES' : 'NO',
            'SUCCESS'
          ]

          return {
            index: globalIndex,
            name: chatName,
            csvRow: csvRow.join(','),
            preCourse: customerResults.preCourse.intent_score,
            totalIncrement,
            success: true
          }

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error)

          // 返回错误行
          const errorRow = [
            chatId,
            `"${chatName}"`,
            'ERROR', 'ERROR', 'ERROR', 'ERROR', 'ERROR',
            'ERROR', 'ERROR', 'ERROR', 'ERROR', 'ERROR', 'ERROR',
            'ERROR', 'ERROR', 'ERROR', 'ERROR', 'ERROR',
            `"${errorMessage}"`
          ]

          return {
            index: globalIndex,
            name: chatName,
            csvRow: errorRow.join(','),
            preCourse: 'ERROR',
            totalIncrement: 'ERROR',
            error: errorMessage,
            success: false
          }
        }
      })

      // 等待当前小批次完成
      const batchResults = await Promise.all(batchPromises)

      // 处理结果
      batchResults.forEach((result) => {
        results.push(result.csvRow)
        if (result.success) {
          successCount++
          console.log(`   ✅ [${result.index}] ${result.name}: ${result.preCourse}→${result.totalIncrement}`)
        } else {
          errorCount++
          console.log(`   ❌ [${result.index}] ${result.name}: ${result.error}`)
        }
      })

      const batchTime = ((Date.now() - batchStartTime) / 1000).toFixed(1)
      console.log(`   📊 小批完成: 成功${batchResults.filter((r) => r.success).length}, 失败${batchResults.filter((r) => !r.success).length}, 耗时${batchTime}秒\n`)
    }

    // 组装最终CSV内容并写入文件
    csvContent += `${results.join('\n')  }\n`
    fs.writeFileSync(csvFilePath, csvContent, 'utf8')

    // 统计结果
    const fileSize = (fs.statSync(csvFilePath).size / 1024).toFixed(2)

    console.log('🎉 批次CSV报告生成完成!')
    console.log(`📁 文件路径: ${csvFilePath}`)
    console.log('📊 处理统计:')
    console.log(`   - 处理范围: ${startIndex + 1} - ${startIndex + batchChats.length}`)
    console.log(`   - 成功处理: ${successCount}`)
    console.log(`   - 处理失败: ${errorCount}`)
    console.log(`   - 成功率: ${((successCount / batchChats.length) * 100).toFixed(1)}%`)
    console.log(`📋 文件大小: ${fileSize} KB`)
    console.log(`⏰ 完成时间: ${new Date().toLocaleString()}`)

    console.log('\n🎯 下一步操作:')
    if (endIndex < allChats.length) {
      const remaining = allChats.length - endIndex
      const nextBatchSize = Math.min(remaining, batchSize)
      console.log(`- 继续处理下一批: ts-node script/generate_intent_csv_batch.ts ${courseNo} ${endIndex} ${nextBatchSize} ${concurrency}`)
    } else {
      console.log('- 所有批次处理完成，可以合并CSV文件')
      console.log(`- 合并命令: ts-node script/merge_csv.ts ${courseNo}`)
    }

  } catch (error) {
    console.error('❌ 生成批次CSV报告失败:', error)
    process.exit(1)
  }
}

// 运行脚本
if (require.main === module) {
  console.log('🚀 分段并行累积意向度CSV生成器')
  console.log('使用方法: ts-node script/generate_intent_csv_batch.ts [期数] [开始位置] [数量] [并发数]')
  console.log('分段示例:')
  console.log('  第1段: ts-node script/generate_intent_csv_batch.ts 78 0 500 50')
  console.log('  第2段: ts-node script/generate_intent_csv_batch.ts 78 500 500 50')
  console.log('  第3段: ts-node script/generate_intent_csv_batch.ts 78 1000 500 50\n')

  generateIntentCSVBatch().catch(console.error)
}

export { generateIntentCSVBatch }
